<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cosmos-Inspired Filterable Grid</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- GSAP Library & Plugins -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/Flip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.13.0/ScrollTrigger.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.13.0/dist/ScrollSmoother.min.js"></script>
    <style>
        html, body {
            height: 100vh;
            overflow: hidden; /* Prevents all scrolling on the body */
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0a0a0a;
            color: #e4e4e4;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .header-font {
            font-family: 'Lora', serif;
        }

        /* Main container for horizontal scrolling */
        .scroll-container {
            width: 100%;
            overflow-x: auto;
            flex-grow: 1; /* Allows this container to fill available space */
            padding-bottom: 1.5rem; /* For scrollbar visibility */
        }
        
        /* Custom scrollbar styles */
        .scroll-container::-webkit-scrollbar {
            height: 8px;
        }
        .scroll-container::-webkit-scrollbar-track {
            background: #1a1a1a;
            border-radius: 4px;
        }
        .scroll-container::-webkit-scrollbar-thumb {
            background: #4a4a4a;
            border-radius: 4px;
        }
        .scroll-container::-webkit-scrollbar-thumb:hover {
            background: #6a6a6a;
        }

        /* The grid itself needs to fill the container's height */
        #bento-grid {
            height: 100%;
            /* Explicitly define 4 rows for the grid layout */
            grid-template-rows: repeat(4, 200px);
        }

        .grid-item {
            visibility: hidden; /* Initial state for load-in animation */
            background-color: #1a1a1a;
            border-radius: 0.75rem; /* 12px */
            overflow: hidden;
            position: relative;
            transform-style: preserve-3d;
            transform: perspective(1000px);
            cursor: pointer;
        }
        
        .grid-item[data-hidden="true"] {
            display: none;
        }

        /* Parallax Layer Styling */
        .parallax-layer {
            position: absolute;
            top: -5%; /* Position outside the frame to hide edges during movement */
            left: -5%;
            width: 110%;
            height: 110%;
            background-size: cover;
            background-position: center;
        }

        .layer-title {
            z-index: 4;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-family: 'Lora', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0,0,0,0.5);
        }

        .layer-fg { z-index: 3; }
        .layer-mg { z-index: 2; }
        .layer-bg { z-index: 1; }
        
        .filter-btn {
            background-color: transparent;
            color: #888;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .filter-btn.active {
            background-color: #2a2a2a;
            color: #fff;
        }

        /* Mobile Adjustments */
        @media (max-width: 768px) {
            #bento-grid {
                grid-template-rows: repeat(4, 150px);
            }
            .layer-title {
                font-size: 1.75rem;
            }
        }
        
        /* Permission Modal */
        #permission-modal {
            background-color: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
        }
        
        /* Gradient Fade */
        #left-gradient {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 100px;
            background: linear-gradient(to right, #0a0a0a, transparent);
            z-index: 5;
            pointer-events: none;
            opacity: 0;
        }

        /* Fullscreen Modal Styles */
        #fullscreen-modal {
            position: fixed;
            inset: 20px; /* 20px margin */
            z-index: 100;
            visibility: hidden; /* Initially hidden */
            transform-style: preserve-3d;
            perspective: 1000px;
            opacity: 0;
        }

        #fullscreen-content {
            width: 100%;
            height: 100%;
            background-color: #1a1a1a;
            border-radius: 0.75rem;
            overflow-x: auto;
            overflow-y: hidden;
            position: relative;
            padding: 2rem;
            display: flex;
            align-items: center;
        }

        .fullscreen-detail-content {
            min-width: 100%;
            width: max-content;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0;
        }

        .fullscreen-detail-content h2 {
            white-space: nowrap;
            margin-bottom: 2rem;
            align-self: flex-start;
        }

        .fullscreen-content-body {
            display: flex;
            gap: 2rem;
            align-items: flex-start;
            width: 100%;
        }

        .fullscreen-image {
            width: 200px;
            height: 200px;
            border-radius: 0.5rem;
            object-fit: cover;
            flex-shrink: 0;
        }

        .fullscreen-text {
            flex: 1;
            max-width: 60vw;
        }

        .fullscreen-text p {
            white-space: normal;
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        #fullscreen-close-btn {
            position: absolute;
            top: 1rem;
            right: 1.5rem;
            font-size: 2.5rem;
            color: white;
            cursor: pointer;
            z-index: 110;
            opacity: 0;
            pointer-events: none;
            text-shadow: 0 0 10px black;
        }

        /* Style for blurring the background content */
        #content-wrapper.blurred {
            filter: blur(10px);
            transform: scale(0.98);
            transition: all 0.5s ease-out;
        }
        
        /* Admin UI Styles */
        .admin-toggle-btn {
            display: none; /* Hidden by default */
            position: absolute;
            top: 0.5rem;
            left: 0.5rem;
            z-index: 20;
            padding: 0.5rem;
            background-color: rgba(0,0,0,0.5);
            border-radius: 9999px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .admin-toggle-btn:hover {
            background-color: rgba(0,0,0,0.8);
        }
        #content-wrapper.admin-mode .admin-toggle-btn {
            display: block; /* Show when in admin mode */
        }
        .grid-item[data-visible="false"] .visible-icon {
            display: none;
        }
        .grid-item[data-visible="true"] .hidden-icon {
            display: none;
        }


    </style>
</head>
<body class="antialiased">

    <!-- Permission Modal for Gyroscope -->
    <div id="permission-modal" class="fixed inset-0 z-50 flex-col items-center justify-center hidden">
        <div class="text-center text-white p-8">
            <h2 class="text-2xl font-bold mb-4">Motion-Based Effects</h2>
            <p class="mb-6">Please grant permission to use your device's motion sensors for a more immersive experience.</p>
            <button id="permission-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full">Enable Motion</button>
        </div>
    </div>

    <!-- Fullscreen Modal Container -->
    <div id="fullscreen-modal">
        <div id="fullscreen-content"></div>
        <div id="fullscreen-close-btn">&times;</div>
    </div>


    <!-- Main Content Wrapper: Flex container to control layout -->
    <div id="content-wrapper" class="relative z-10 container mx-auto px-4 h-full flex flex-col pt-8 sm:pt-12 transition-all duration-500">
        
        <!-- Gradient Overlay -->
        <div id="left-gradient"></div>

        <!-- Filter Buttons -->
        <div id="filter-buttons" class="flex justify-center items-center gap-2 mb-8 sm:mb-12 flex-shrink-0">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="art">Art</button>
            <button class="filter-btn" data-filter="architecture">Architecture</button>
            <button class="filter-btn" data-filter="nature">Nature</button>
            <button class="filter-btn" data-filter="tech">Tech</button>
        </div>

        <!-- Horizontal Scroll Container -->
        <div class="scroll-container">
            <!-- Bento Grid: Now with 3 rows -->
            <div id="bento-grid" class="grid grid-flow-col auto-cols-[200px] gap-4">
                
                 <!-- Column 1 -->
                <div class="grid-item col-span-2 row-span-3" data-category="art" data-visible="true">
                    <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Art</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x600/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x600/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x600/4682b4/000000?text=BG')"></div>
                    <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Essence of Art</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/ff6347/ffffff?text=Art" alt="Art" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Art is a diverse range of human activity, and resulting product, that involves creative or imaginative talent expressive of technical proficiency, beauty, emotional power, or conceptual ideas.</p>
                                <p class="text-lg text-gray-300">There is no generally agreed definition of what constitutes art, and its interpretation has varied greatly throughout history and in different cultures.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Column 2 -->
                <div class="grid-item col-span-2 row-span-2" data-category="architecture" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Arch</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Principles of Architecture</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/20b2aa/ffffff?text=Architecture" alt="Architecture" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Architecture is both the process and the product of planning, designing, and constructing buildings or other structures. Architectural works, in the material form of buildings, are often perceived as cultural symbols and as works of art.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-item col-span-2 row-span-1" data-category="nature" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Nature</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Beauty of Nature</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/228b22/ffffff?text=Nature" alt="Nature" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Nature, in the broadest sense, is the physical world or universe. "Nature" can refer to the phenomena of the physical world, and also to life in general. The study of nature is a large, if not the only, part of science.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column 3 -->
                <div class="grid-item col-span-1 row-span-1" data-category="tech" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Tech</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The World of Tech</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/4682b4/ffffff?text=Tech" alt="Technology" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Technology is the sum of any techniques, skills, methods, and processes used in the production of goods or services or in the accomplishment of objectives, such as scientific investigation.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-item col-span-1 row-span-2" data-category="art" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Sculpture</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x400/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x400/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x400/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Forms in Sculpture</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/daa520/ffffff?text=Sculpture" alt="Sculpture" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Sculpture is the branch of the visual arts that operates in three dimensions. It is one of the plastic arts. Durable sculptural processes originally used carving and modelling, in stone, metal, ceramics, wood and other materials.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column 4 -->
                 <div class="grid-item col-span-1 row-span-1" data-category="nature" data-visible="true">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Ocean</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Deep Ocean</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/006994/ffffff?text=Ocean" alt="Ocean" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">The ocean is a body of water that composes much of a planet's hydrosphere. On Earth, an ocean is one of the major conventional divisions of the World Ocean.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-item col-span-1 row-span-1" data-category="architecture" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Brutalism</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Brutalist Architecture</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/696969/ffffff?text=Brutalism" alt="Brutalism" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Brutalist architecture is a style that emerged in the 1950s and grew out of the early-20th century modernist movement. Brutalist buildings are characterised by their massive, monolithic and 'blocky' appearance with a rigid geometric style and large-scale use of poured concrete.</p>
                            </div>
                        </div>
                    </div>
                </div>
                 <div class="grid-item col-span-1 row-span-1" data-category="tech" data-visible="true">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Data</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/200x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/200x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/200x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Power of Data</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/800080/ffffff?text=Data" alt="Data" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Data are individual facts, statistics, or items of information, often numeric. In a more technical sense, data are a set of values of qualitative or quantitative variables about one or more persons or objects.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column 5 -->
                <div class="grid-item col-span-2 row-span-3" data-category="nature" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Forest</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x600/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x600/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x600/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">Into the Forest</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/228b22/ffffff?text=Forest" alt="Forest" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">A forest is an area of land dominated by trees. Hundreds of definitions of forest are used throughout the world, incorporating factors such as tree density, tree height, land use, legal standing and ecological function.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Column 6 -->
                 <div class="grid-item col-span-2 row-span-2" data-category="art" data-visible="true">
                      <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Painting</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x400/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x400/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x400/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Art of Painting</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/dc143c/ffffff?text=Painting" alt="Painting" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">Painting is the practice of applying paint, pigment, color or other medium to a solid surface. The medium is commonly applied to the base with a brush, but other implements, such as knives, sponges, and airbrushes, can be used.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="grid-item col-span-2 row-span-1" data-category="tech" data-visible="true">
                     <div class="admin-toggle-btn">
                        <svg class="visible-icon w-5 h-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" /><path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <svg class="hidden-icon w-5 h-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>
                    </div>
                    <div class="parallax-layer layer-title">Code</div>
                    <div class="parallax-layer layer-fg" style="background-image: url('https://placehold.co/400x200/ff6347/000000?text=FG')"></div>
                    <div class="parallax-layer layer-mg" style="background-image: url('https://placehold.co/400x200/20b2aa/000000?text=MG')"></div>
                    <div class="parallax-layer layer-bg" style="background-image: url('https://placehold.co/400x200/4682b4/000000?text=BG')"></div>
                     <div class="hidden fullscreen-detail-content">
                        <h2 class="text-4xl font-bold font-serif text-white">The Language of Code</h2>
                        <div class="fullscreen-content-body">
                            <img src="https://placehold.co/200x200/000080/ffffff?text=Code" alt="Code" class="fullscreen-image">
                            <div class="fullscreen-text">
                                <p class="text-lg text-gray-300">In computing, source code is any collection of code, with or without comments, written using a human-readable programming language, usually as plain text. The source code of a program is specially designed to facilitate the work of computer programmers.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            gsap.registerPlugin(Flip, ScrollTrigger);

            const contentWrapper = document.getElementById('content-wrapper');
            const grid = document.getElementById('bento-grid');
            const scrollContainer = document.querySelector('.scroll-container');
            const gridItems = gsap.utils.toArray('.grid-item');
            const filterButtonsContainer = document.getElementById('filter-buttons');
            const buttons = gsap.utils.toArray('.filter-btn');
            const permissionModal = document.getElementById('permission-modal');
            const permissionBtn = document.getElementById('permission-btn');
            const leftGradient = document.getElementById('left-gradient');
            
            // Fullscreen Modal elements
            const fullscreenModal = document.getElementById('fullscreen-modal');
            const fullscreenContent = document.getElementById('fullscreen-content');
            const fullscreenCloseBtn = document.getElementById('fullscreen-close-btn');
            let activeItem = null;
            let isAnimating = false;
            let isAdminMode = false;

            // Adjust grid for mobile
            if (window.matchMedia("(max-width: 768px)").matches) {
                grid.style.autoColumns = '150px';
            }

            // --- Horizontal Scroll with Mouse Wheel ---
            scrollContainer.addEventListener('wheel', (evt) => {
                if(isAnimating) {
                    evt.preventDefault();
                    return;
                }
                evt.preventDefault();
                gsap.to(scrollContainer, {
                    scrollLeft: scrollContainer.scrollLeft + evt.deltaY,
                    ease: "power1.out",
                    duration: 0.5
                });
            });

            // --- Initial Page Load Animation ---
            gsap.from(gridItems, {
                duration: 1,
                opacity: 0,
                scale: 0.5,
                x: 100,
                ease: "power3.out",
                stagger: { each: 0.08, from: "end" },
                onStart: () => {
                    gsap.set(gridItems, { visibility: 'visible' });
                }
            });

            // --- Scroll-based Gradient ---
            function setupScrollAnimations() {
                ScrollTrigger.getAll().forEach(st => st.kill());

                ScrollTrigger.create({
                    trigger: scrollContainer,
                    scroller: scrollContainer,
                    horizontal: true,
                    start: "left left",
                    end: "right right",
                    onUpdate: self => {
                        gsap.to(leftGradient, { opacity: self.progress > 0.01 ? 1 : 0, duration: 0.3 });
                    },
                });
            }
            
            setupScrollAnimations();

            // --- Filter & Admin Logic ---
            function applyFilterAndAnimate(filter) {
                const state = Flip.getState(gridItems, {props: "display"});

                gridItems.forEach(item => {
                    const categoryMatch = (filter === 'all' || item.dataset.category === filter);
                    const isVisibleByAdmin = item.dataset.visible === 'true';
                    item.dataset.hidden = !(categoryMatch && isVisibleByAdmin);
                });

                Flip.from(state, {
                    duration: 0.7,
                    scale: true,
                    ease: "power1.inOut",
                    stagger: 0.05,
                    absolute: true,
                    onEnter: elements => gsap.fromTo(elements, { opacity: 0, scale: 0.8 }, { opacity: 1, scale: 1, duration: 0.6 }),
                    onLeave: elements => gsap.to(elements, { opacity: 0, scale: 0.8, duration: 0.6 }),
                    onComplete: setupScrollAnimations
                });
            }

            filterButtonsContainer.addEventListener('click', (e) => {
                const btn = e.target.closest('.filter-btn');
                if (!btn || isAnimating) return;

                buttons.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                applyFilterAndAnimate(btn.dataset.filter);
            });

            // --- Admin Mode ---
            document.addEventListener('keydown', (e) => {
                if (e.key === 'a') {
                    isAdminMode = !isAdminMode;
                    contentWrapper.classList.toggle('admin-mode', isAdminMode);
                    console.log(`Admin mode ${isAdminMode ? 'enabled' : 'disabled'}.`);
                }
            });

            grid.addEventListener('click', (e) => {
                const toggleBtn = e.target.closest('.admin-toggle-btn');
                if (isAdminMode && toggleBtn) {
                    e.stopPropagation();
                    const item = toggleBtn.closest('.grid-item');
                    const isVisible = item.dataset.visible === 'true';
                    item.dataset.visible = !isVisible;
                    
                    // Visually update icon
                    toggleBtn.querySelector('.visible-icon').style.display = !isVisible ? 'block' : 'none';
                    toggleBtn.querySelector('.hidden-icon').style.display = isVisible ? 'block' : 'none';

                    const currentFilter = filterButtonsContainer.querySelector('.active').dataset.filter;
                    applyFilterAndAnimate(currentFilter);
                }
            });
            
            // --- Fullscreen Logic ---
            function openFullscreen(item) {
                if (isAnimating || isAdminMode) return;
                isAnimating = true;
                activeItem = item;

                contentWrapper.classList.add('blurred');

                const detailContent = item.querySelector('.fullscreen-detail-content');
                fullscreenContent.innerHTML = detailContent ? detailContent.innerHTML : '<p>No details available.</p>';
                
                const state = Flip.getState(item);
                
                gsap.set(fullscreenModal, { visibility: 'visible', opacity: 1 });
                gsap.set(item, { visibility: 'hidden' });
                
                Flip.from(state, {
                    target: fullscreenModal,
                    duration: 0.8,
                    ease: "power2.inOut",
                    absolute: true,
                    onComplete: () => {
                        isAnimating = false;
                        gsap.to(fullscreenCloseBtn, { autoAlpha: 1, pointerEvents: 'auto', duration: 0.3 });
                    }
                });
            }

            function closeFullscreen() {
                if (isAnimating || !activeItem) return;
                isAnimating = true;
                
                contentWrapper.classList.remove('blurred');

                gsap.to(fullscreenCloseBtn, { autoAlpha: 0, pointerEvents: 'none', duration: 0.2 });
                
                gsap.to(fullscreenModal, {
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.in",
                    onComplete: () => {
                        gsap.set(fullscreenModal, { visibility: 'hidden' });
                        gsap.set(activeItem, { visibility: 'visible' });
                        fullscreenContent.innerHTML = '';
                        activeItem = null;
                        isAnimating = false;
                    }
                });
            }

            gridItems.forEach(item => {
                item.addEventListener('click', () => openFullscreen(item));
            });
            fullscreenCloseBtn.addEventListener('click', closeFullscreen);



            // -- smooth scrolling --

             // use a script tag or an external JS file
             document.addEventListener("scroll-container", (event) => {
             gsap.registerPlugin(ScrollTrigger,ScrollSmoother)
            ScrollSmoother.create({
                smooth: 1, // how long (in seconds) it takes to "catch up" to the native scroll position
                effects: true, // looks for data-speed and data-lag attributes on elements
                smoothTouch: 0.1, 
            });

            });

            // --- Input-based Interaction ---
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

            if (isTouchDevice) {
                // --- Gyroscope Logic ---
                permissionBtn.addEventListener('click', () => {
                    if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                        DeviceOrientationEvent.requestPermission()
                            .then(permissionState => {
                                if (permissionState === 'granted') {
                                    window.addEventListener('deviceorientation', handleOrientation);
                                    permissionModal.style.display = 'none';
                                }
                            })
                            .catch(console.error);
                    } else {
                        window.addEventListener('deviceorientation', handleOrientation);
                        permissionModal.style.display = 'none';
                    }
                });
                
                if (typeof DeviceOrientationEvent.requestPermission === 'function') {
                    permissionModal.style.display = 'flex';
                } else {
                     window.addEventListener('deviceorientation', handleOrientation);
                }

                function handleOrientation(event) {
                    if(activeItem) return;
                    const beta = event.beta;
                    const gamma = event.gamma;
                    const maxTilt = 30;
                    const clampedBeta = gsap.utils.clamp(-maxTilt, maxTilt, beta);
                    const clampedGamma = gsap.utils.clamp(-maxTilt, maxTilt, gamma);
                    const percentY = clampedBeta / maxTilt;
                    const percentX = clampedGamma / maxTilt;
                    const maxRotation = 10;
                    
                    gridItems.forEach(item => {
                        if (item.dataset.hidden !== "true") {
                            const title = item.querySelector('.layer-title');
                            const fg = item.querySelector('.layer-fg');
                            const mg = item.querySelector('.layer-mg');
                            const bg = item.querySelector('.layer-bg');

                            gsap.to(item, { rotationX: percentY * -maxRotation, rotationY: percentX * maxRotation, ease: "power1.out", duration: 0.7 });
                            gsap.to(title, { x: percentX * 30, y: percentY * 30, ease: "power1.out", duration: 0.7 });
                            gsap.to(fg, { x: percentX * 20, y: percentY * 20, ease: "power1.out", duration: 0.7 });
                            gsap.to(mg, { x: percentX * 10, y: percentY * 10, ease: "power1.out", duration: 0.7 });
                            gsap.to(bg, { x: percentX * 5, y: percentY * 5, ease: "power1.out", duration: 0.7 });
                        }
                    });
                }

            } else {
                // --- Mouse Logic ---
                gridItems.forEach(item => {
                    const title = item.querySelector('.layer-title');
                    const fg = item.querySelector('.layer-fg');
                    const mg = item.querySelector('.layer-mg');
                    const bg = item.querySelector('.layer-bg');
                    const maxRotation = 10;

                    item.addEventListener('mouseenter', (e) => {
                        if(activeItem) return;
                        gsap.to(item, { zIndex: 10, duration: 0.1 });
                    });

                    item.addEventListener('mousemove', (e) => {
                        if(activeItem) return;
                        const rect = item.getBoundingClientRect();
                        const x = e.clientX - rect.left;
                        const y = e.clientY - rect.top;
                        const centerX = rect.width / 2;
                        const centerY = rect.height / 2;
                        const percentX = (x - centerX) / centerX;
                        const percentY = (y - centerY) / centerY;

                        gsap.to(item, { rotationX: percentY * -maxRotation, rotationY: percentX * maxRotation, scale: 1.05, ease: "power1.out", duration: 0.7 });
                        gsap.to(title, { x: percentX * 30, y: percentY * 30, ease: "power1.out", duration: 0.7 });
                        gsap.to(fg, { x: percentX * 20, y: percentY * 20, ease: "power1.out", duration: 0.7 });
                        gsap.to(mg, { x: percentX * 10, y: percentY * 10, ease: "power1.out", duration: 0.7 });
                        gsap.to(bg, { x: percentX * 5, y: percentY * 5, ease: "power1.out", duration: 0.7 });
                    });

                    item.addEventListener('mouseleave', (e) => {
                        if(activeItem) return;
                        const tl = gsap.timeline({ defaults: { ease: "power3.out", duration: 1, overwrite: 'auto' } });
                        tl.to(item, { rotationX: 0, rotationY: 0, scale: 1, zIndex: 1 }, 0);
                        tl.to([title, fg, mg, bg], { x: 0, y: 0 }, 0);
                    });
                });
            }
        });
    </script>

</body>
</html>
